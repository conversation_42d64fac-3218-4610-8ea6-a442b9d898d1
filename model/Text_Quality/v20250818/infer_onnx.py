import os
import time
import json
import numpy as np
import torch
import onnxruntime as ort
from transformers import BertTokenizer
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
import argparse
import torch.nn.functional as F


# 自定义数据集类
class RankingDataset(Dataset):
    def __init__(self, file_path, tokenizer, max_length_limit):
        self.data = []
        self.tokenizer = tokenizer
        self.max_length_limit = max_length_limit
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                data = json.loads(line.strip())
                self.data.append(data)
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        content = item['content']
        # score = item['score']

        # 编码
        encoded_inputs = self.tokenizer(
            query,
            padding=False,
            truncation=True,
            max_length=self.max_length_limit,
            return_tensors='pt'
        )

        # 处理编码后的输入, 维度:[max_length]
        input_ids = encoded_inputs['input_ids'].squeeze(0)
        attention_mask = encoded_inputs['attention_mask'].squeeze(0)
        token_type_ids = encoded_inputs.get('token_type_ids', None)
        token_type_ids = token_type_ids.squeeze(0) if token_type_ids is not None else None

        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'token_type_ids': token_type_ids,
            # 'score': score
            'original_data': item # 保存原始数据用于结果输出
        }


# 自定义collate函数动态统一一个batch内的序列长度
def custom_collate(batch):
    # 1. 计算当前batch的最大序列长度, 第i个样本的input_ids形状是[num_docs(i), max_length(i)]
    max_length = max(item['input_ids'].size(0) for item in batch)

    # 2. 初始化存储列表
    input_ids_list = []
    attention_mask_list = []
    token_type_ids_list = []
    # scores_list = []
    original_data_list = [item['original_data'] for item in batch]

    # 3. 对每个样本进行填充
    for item in batch:
        input_ids = item['input_ids']
        attention_mask = item['attention_mask']
        token_type_ids = item['token_type_ids']
        # score = item['score']

        # a. 填充序列长度到max_max_length
        padding_length = max_length - input_ids.size(0)
        if padding_length > 0:
            # 填充input_ids
            input_ids = F.pad(input_ids, (0, padding_length), mode='constant', value=0)
            # 填充attention_mask
            attention_mask = F.pad(attention_mask, (0, padding_length), mode='constant', value=0)
            # 填充token_type_ids
            token_type_ids = F.pad(token_type_ids, (0, padding_length), mode='constant', value=0) if token_type_ids is not None else None

        # 4. 添加到列表
        input_ids_list.append(input_ids)
        attention_mask_list.append(attention_mask)
        token_type_ids_list.append(token_type_ids) if token_type_ids is not None else None
        # scores_list.append(score)

    # 5. 堆叠为batch tensor
    input_ids_batch = torch.stack(input_ids_list)
    attention_mask_batch = torch.stack(attention_mask_list)
    token_type_ids_batch = torch.stack(token_type_ids_list) if token_type_ids_list else None
    # scores_batch = torch.stack(scores_list)

    return {
        'input_ids': input_ids_batch,
        'attention_mask': attention_mask_batch,
        'token_type_ids': token_type_ids_batch,
        # 'scores': scores_batch,
        'original_data': original_data_list
    }


# 推理
def inference_onnx(session, eval_dataloader, device, args, num_classes):
    all_true_scores = [] # 存储所有真实标签
    all_pred_scores = [] # 存储所有预测标签

    # 初始化时间统计变量
    total_inference_time = 0.0
    batch_count = 0
    results = []
    total_mse = 0.0
    total_mae = 0.0
    sample_count = 0
    
    with torch.no_grad():
        for batch in tqdm(eval_dataloader, desc="Evaluating"):
            input_ids = batch['input_ids'].cpu().numpy()
            attention_mask = batch['attention_mask'].cpu().numpy()
            token_type_ids = batch['token_type_ids'].cpu().numpy() if batch['token_type_ids'] is not None else None
            original_data = batch['original_data']
            batch_size = input_ids.shape[0]
            sample_count += batch_size

            # 记录推理开始时间
            start_time = time.time()
            
            # 准备输入字典
            inputs = {
                'input_ids': input_ids,
                'attention_mask': attention_mask
            }
            if token_type_ids is not None:
                inputs['token_type_ids'] = token_type_ids
            
            # ONNX推理
            outputs = session.run(None, inputs)
            logits = outputs[0]
            
            # 记录推理结束时间并计算耗时
            end_time = time.time()
            total_inference_time += (end_time - start_time)
            batch_count += 1

            # 将推理结果转换为numpy并添加到原始数据
            pred_scores = logits.flatten()
            for i in range(len(original_data)):
                data = original_data[i]

                true_score = data.get('score')
                pred_score = float(pred_scores[i])

                data['quality_score'] = pred_score
                results.append(data)

                # 保存结果
                all_true_scores.append(true_score)
                all_pred_scores.append(pred_score)

                # 计算当前批次的损失并累积
                pred_tensor = torch.tensor(pred_score, dtype=torch.float32)
                true_tensor = torch.tensor(true_score, dtype=torch.float32)
                total_mse += F.mse_loss(pred_tensor, true_tensor).item()
                total_mae += F.l1_loss(pred_tensor, true_tensor).item()
    
    # 计算平均推理时间
    avg_inference_time = (total_inference_time / batch_count) * 1000 # 转换为毫秒
    print(f"Average Inference Time: {avg_inference_time:.4f} ms per batch")

    # 转换为numpy数组以便计算统计指标
    y_true = np.array(all_true_scores)
    y_pred = np.array(all_pred_scores)
    
    # 计算各种回归指标
    mse = total_mse / sample_count # 均方误差
    rmse = np.sqrt(mse)            # 均方根误差
    mae = total_mae / sample_count # 平均绝对误差
    # r2 = r2_score(y_true, y_pred)                            # 决定系数
    # pearson_r, pearson_p = stats.pearsonr(y_true, y_pred)    # Pearson相关系数
    # spearman_r, spearman_p = stats.spearmanr(y_true, y_pred) # Spearman等级相关系数
    
    return results, {
        "MSE": mse,   # 均方误差
        "RMSE": rmse, # 均方根误差
        "MAE": mae,   # 平均绝对误差
        # "R2": r2,               # 决定系数
        # "PearsonR": pearson_r,  # Pearson相关系数
        # "SpearmanR": spearman_r # Spearman相关系数
    }


def main():
    parser = argparse.ArgumentParser(description='Regression Model Inference')

    # 数据路径参数
    parser.add_argument('--eval_path', type=str, default="/data1/yicheng3/Medicine_Classification_Query/eval.json", help='Path to evaluation data')
    parser.add_argument('--onnx_model_path', type=str, default="/data1/yicheng3/Medicine_Classification_Query/checkpoint/epoch_27_Accuracy_0.9950/Classfication_model.onnx", help='Path to ONNX model')
    parser.add_argument('--tokenizer_path', type=str, default='/data1/yicheng3/Medicine_Classification_Query/checkpoint/epoch_27_Accuracy_0.9950', help='Model output directory')
    parser.add_argument('--output_file', type=str, default='/data1/yicheng3/Medicine_Classification_Query/eval_output.json', help='Model output directory')
    
    # 推理参数
    parser.add_argument('--eval_batch_size', type=int, default=1, help='Evaluation batch size')
    parser.add_argument('--max_length', type=int, default=2048, help='Maximum sequence length')
    
    # GPU参数
    parser.add_argument('--gpu_ids', type=str, default='1', help='GPU ids to use')

    # CPU参数
    parser.add_argument('--num_workers', type=int, default=1, help='Number of worker processes for data loading')
    
    args = parser.parse_args()

    # 设置设备
    device = 'cuda' if ort.get_device() == 'GPU' else 'CPU'
    print(f"Using device: {device}")
    
    # 加载Tokenizer
    tokenizer = BertTokenizer.from_pretrained(args.tokenizer_path)
    
    # 加载数据集
    inference_dataset = RankingDataset(
        args.eval_path,
        tokenizer, 
        args.max_length
    )
    
    inference_dataloader = DataLoader(
        inference_dataset,
        batch_size=args.eval_batch_size,
        num_workers=args.num_workers,
        pin_memory=True,
        shuffle=False,
        collate_fn=custom_collate
    )

    # 加载ONNX模型
    # providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if device == 'cuda' else ['CPUExecutionProvider']
    providers = [('CUDAExecutionProvider', {'device_id': args.gpu_ids}), 'CPUExecutionProvider'] if device == 'cuda' else ['CPUExecutionProvider']
    session = ort.InferenceSession(args.onnx_model_path, providers=providers)

    # 推理
    results, eval_results = inference_onnx(
        session, 
        inference_dataloader, 
        device,
    )
    for key, value in eval_results.items():
        print(f"Evaluation {key}: {value:.4f}")
    
    # 保存结果
    with open(args.output_file, 'w', encoding='utf-8') as f:
        for result in results:
            f.write(json.dumps(result, ensure_ascii=False) + '\n')
    print(f"Results saved to {args.output_file}")

if __name__ == "__main__":
    main()

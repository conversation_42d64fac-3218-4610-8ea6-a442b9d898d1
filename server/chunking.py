from vendor.pandora.context.pandora_context import PandoraContext
from server.proto.chunking import RequestBody, ResponseBody, PayloadResponse
import os
import re
import json
import glob
import concurrent.futures
from tqdm import tqdm
from server.chunk import strategy_select


async def chunking_process(pc: PandoraContext, frequest: RequestBody) -> ResponseBody:
    form = await frequest.json()
    request = RequestBody(**form)
    payload = request.payload
    filter_model = payload.filter_model
    chunk_model = payload.chunk_model
    docs = payload.docs
    results = []
    for doc in docs:
        #  markdown数据清洗和切分
        need_chunk_data = []
        md_content = doc.get("content")
        chunks = preprocess(md_content)
        for i,chunk in enumerate(chunks):
            need_chunk_data.append({
                "title": chunk.get('title', ''),
                "content": chunk.get('text_deal', ''),
                "chunk_id": i+1
            })
        # 文档切分
        doc_chunks = strategy_select(need_chunk_data,chunk_model)
        results.extend(doc_chunks)
        # 低质过滤 - 调用onnx模型

        # title改写 - 调用第三方http接口
        
    return ResponseBody(header=request.header, payload=PayloadResponse(filter_model=filter_model, chunk_model=chunk_model, results=results))


def is_english(text):
    """检查文本是否全为英文字符（不含中文）"""
    return not re.search(r'[\u4e00-\u9fa5]', text)
def preprocess(md_content: str) :
    """
    切分文本
    """
    blocks = []
    current_block = []
    lines = md_content.split('\n')
    
    for line in lines:
        # 修改：跳过所有英文摘要和关键词
        stripped_line = line.strip()
        if stripped_line in ['Abstract', 'Keywords'] or is_english(stripped_line):
            continue
            
        # 只处理中文的摘要和关键词
        if stripped_line in ['摘要', '关键词']:
            if current_block:
                blocks.append('\n'.join(current_block))
                current_block = []
            blocks.append(f"## {stripped_line}")  # 作为二级标题
            continue
            
        if line.startswith('#') and line.strip():
            if current_block:
                blocks.append('\n'.join(current_block))
                current_block = []
            blocks.append(line)
        else:
            # 不再跳过英文标题行
            if line.strip() or current_block:
                current_block.append(line)
        
    if current_block:
        blocks.append('\n'.join(current_block))

    # 处理标题层级和内容
    output_jsonl = []
    title_stack = []  # 标题栈
    main_title = ""   # 当前主标题（一级标题）
    content_buffer = []
    current_level = 0
    has_main_title = False  # 标记是否已找到当前部分的主标题
    collecting_main_content = False  # 是否正在收集主标题内容
    section_count = 0  # 记录一级标题部分数量
    start_collecting = False  # 标记是否开始收集内容（遇到第一个标题后开始）
    pre_title_content = []   # 存储第一个标题前的内容

    def flush_content(force=False):
        """将缓冲区内容写入JSONL"""
        nonlocal content_buffer, title_stack
        # 修改点：移除了对content_buffer的检查，允许空内容
        if not title_stack and not force:
            content_buffer = []
            return
            
        full_content = '\n'.join(content_buffer)
        # 不再调用remove_english_sections
        full_content = re.sub(r'\n{3,}', '\n\n', full_content)
        full_content = re.sub(r'\n{2,}$', '\n', full_content)
        full_content = full_content.strip()
        
        # 确保标题栈中始终包含当前主标题
        if has_main_title and main_title and main_title not in title_stack:
            title_stack.insert(0, main_title)
        
        full_title = '_'.join(title_stack) if title_stack else main_title
        
        if has_main_title and   (full_title or force):
            output_jsonl.append({
                "title": full_title,
                "text_deal": full_content
            })
        
        content_buffer = []
    
    for block in blocks:
        # 处理标题行
        if block.startswith('#'):
            # 如果是第一个标题，先处理标题前的内容
            if not start_collecting and pre_title_content:
                content_str = '\n'.join(pre_title_content).strip()
                content_str = re.sub(r'\n{3,}', '\n\n', content_str)
                content_str = re.sub(r'\n{2,}$', '\n', content_str)
                content_str = content_str.strip()
                if content_str:
                    output_jsonl.append({
                        "title": "",
                        "text_deal": content_str
                    })
                pre_title_content = []
                start_collecting = True
                
            match = re.match(r'^(#+)\s*(.*)', block)
            if not match:
                continue
                
            level = len(match.group(1))
            title_text = match.group(2).strip()
            
            # 修改：跳过所有英文标题
            if is_english(title_text):
                continue
            
            # 检查是否是参考文献标题（支持多种写法）
            is_reference = re.sub(r'\s+', '', title_text) in ["参考文献", "参考书目", "参考资料", "參考文獻"]
            if is_reference and has_main_title:
                # 刷新之前的内容
                flush_content()
                # 参考文献标题独立，但属于当前主标题部分
                title_stack = [main_title, "参考文献"]
                current_level = level
                continue
            
            # 处理一级标题（#开头）- 新的独立部分
            if level == 1:
                # 如果已经有处理过的内容，先flush当前部分
                if has_main_title:
                    flush_content()
                    # 重置当前部分的状态
                    title_stack = []
                    current_level = 0
                    collecting_main_content = False
                
                # 设置新的主标题
                main_title = title_text
                title_stack = [main_title]
                current_level = level
                has_main_title = True
                collecting_main_content = True  # 开始收集新主标题的内容
                section_count += 1
                # 开始收集内容
                start_collecting = True
                continue
                
            # 第一个中文标题作为主标题（适用于没有明确一级标题的情况）
            if not has_main_title:
                main_title = title_text
                title_stack = [main_title]
                current_level = level
                has_main_title = True
                collecting_main_content = True  # 开始收集主标题内容
                # 开始收集内容
                start_collecting = True
                continue
            
            # 关键修改：在遇到新标题时，先刷新当前内容
            # 这确保了上级标题和下级标题之间的内容不会被遗漏
            flush_content()
                
            # 处理主标题后的第一个标题（通常是摘要）
            if collecting_main_content:
                collecting_main_content = False  # 停止收集主标题内容
                # 重置标题栈，包含主标题
                title_stack = [main_title, title_text]
                current_level = level
                continue
                
            # 调整标题层级栈 - 确保不会弹出主标题
            if level > current_level:
                # 下级标题 - 直接添加到栈中
                title_stack.append(title_text)
            elif level < current_level:
                # 上级标题 - 弹出直到匹配当前级别，保留主标题
                while level < current_level and len(title_stack) > 1:
                    title_stack.pop()
                    current_level -= 1
                # 替换当前标题
                if title_stack:
                    title_stack[-1] = title_text
            else:
                # 同级标题 - 替换栈顶
                if title_stack:
                    title_stack[-1] = title_text
                
            current_level = level
            # 重置内容缓冲区，只收集当前标题后的内容
            content_buffer = []
            
        else:
            # 处理内容块
            clean_block = block.strip()
            if clean_block:
                # 跳过英文内容块
                if is_english(clean_block):
                    continue
                
                if not start_collecting:
                    # 在第一个标题之前，收集到pre_title_content
                    pre_title_content.append(clean_block)
                else:
                    # 在第一个标题之后，收集到content_buffer
                    content_buffer.append(clean_block)
    
    # 处理最后一个内容块
    if has_main_title:
        flush_content(force=True)
    
    # 处理整个文件都没有标题的情况
    if not start_collecting and pre_title_content:
        content_str = '\n'.join(pre_title_content).strip()
        content_str = re.sub(r'\n{3,}', '\n\n', content_str)
        content_str = re.sub(r'\n{2,}$', '\n', content_str)
        content_str = content_str.strip()
        if content_str:
            output_jsonl.append({
                "title": "",
                "text_deal": content_str
            })
    return output_jsonl
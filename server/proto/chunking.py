from pydantic import BaseModel, Field
from typing import List,Optional,Dict,Any

class Header(BaseModel):
    traceId: Optional[str] = ""
    appid: Optional[str] = ""


class PayloadRequest(BaseModel):
    filter_model: Optional[str] = ""
    chunk_model: Optional[str] = ""
    docs: List[dict] = []

class RequestBody(BaseModel):
    header: Header 
    payload: PayloadRequest 

class PayloadResponse(BaseModel):
    filter_model: Optional[str] = ""
    chunk_model: Optional[str] = ""
    results: List[dict] = []

class ResponseBody(BaseModel):
    header: Header
    payload: PayloadResponse 
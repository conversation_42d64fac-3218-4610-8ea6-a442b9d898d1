import re
import time
import random
from datetime import datetime, timezone, timedelta

# ------------------------------------对于\n前没有中文标点符号合并的段落不进行后续的按照标点符号切分-----------------------------

__all__ = ["chunk", "load_json", "save_json", "find_all_json", "format_magazine","format_guide_textbook"]

# 预编译正则表达式以提高性能
RE_SPLIT_PATTERN = re.compile(r'\n')
RE_CHINESE_PUNCTUATION = re.compile(r'[。！？]$')
RE_ENGLISH_CHAR_CHECK = re.compile(r'[a-zA-Z0-9,.;:!?)]')
RE_CHINESE_CHAR_CHECK = re.compile(r'[\u4e00-\u9fff]')
REFERENCE_PATTERNS = [
    re.compile(r'参\s*考\s*文\s*献'),  # 按顺序,允许空格
    re.compile(r"微信订阅")
]

def chunk_long_doc(string: str, max_length: int, split: str, merge: str) -> list[str]:
    if split:
        re_split = re.compile(split)
        # 按照标识符切分原始字符串,并过滤空片段
        paragraphs = [part.strip() for part in re_split.split(string) if part.strip()]
    else:
        paragraphs = [string]
    
    result = []
    current_group = []
    current_length = 0

    # 遍历所有段落进行合并
    for para in paragraphs:
        para_length = len(para)
        # 检查加入当前段落后是否会超过长度限制
        if current_length + para_length > max_length:
            # 如果会超过,就把当前组加入结果
            if current_group:
                result.append(merge.join(current_group))
            # 开始新的组
            current_group = [para]
            current_length = para_length
        else:
            # 不会超过就加入当前组
            current_group.append(para)
            current_length += para_length
    
    # 添加最后一组
    if current_group:
        result.append(merge.join(current_group))
    
    return result

def to_timestamp(date_str):
    # 定义北京时区 (UTC+8)
    beijing_tz = timezone(timedelta(hours=8))
    
    # 处理空字符串情况
    if not date_str:
        return 0
    
    # 处理纯年份(4位数字)
    if isinstance(date_str, int) or (isinstance(date_str, str) and date_str.isdigit() and len(date_str) == 4):
        year = int(date_str)
        # 北京时区的该年1月1日0时0分0秒
        dt = datetime(year, 1, 1, 0, 0, 0, tzinfo=beijing_tz)
        return int(dt.timestamp())
    
    # 解析带"年"字的年份格式（如2015年）
    try:
        dt = datetime.strptime(date_str, "%Y年")
        # 北京时区的该年1月1日0时0分0秒
        dt = dt.replace(month=1, day=1, hour=0, minute=0, second=0, tzinfo=beijing_tz)
        return int(dt.timestamp())
    except ValueError:
        pass  # 解析失败，继续尝试其他格式
    
    # 新增：解析"2019年9月"这类中文年月格式
    try:
        dt = datetime.strptime(date_str, "%Y年%m月")
        # 补全为当月1日0时0分0秒（北京时区）
        dt = dt.replace(day=1, hour=0, minute=0, second=0, tzinfo=beijing_tz)
        return int(dt.timestamp())
    except ValueError:
        pass  # 解析失败，继续尝试其他格式
    
    # 尝试解析中文日期格式 (YYYY年MM月DD日)
    try:
        cleaned = date_str.replace(" ", "")
        dt = datetime.strptime(cleaned, "%Y年%m月%d日")
        # 设置为北京时区的该日0时0分0秒
        dt = dt.replace(hour=0, minute=0, second=0, tzinfo=beijing_tz)
        return int(dt.timestamp())
    except ValueError:
        pass  # 解析失败，继续尝试其他格式
    
    # 尝试解析 年月日时分秒格式 (2017-07-21 19:08:35)
    try:
        dt = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        # 设置为北京时区
        dt = dt.replace(tzinfo=beijing_tz)
        return int(dt.timestamp())
    except ValueError:
        pass  # 解析失败，继续尝试其他格式
    
    # 尝试解析 年月格式 (2017-07)，默认当月第一天0时
    try:
        dt = datetime.strptime(date_str, "%Y-%m")
        # 设置为北京时区的当月1日0时0分0秒
        dt = dt.replace(day=1, hour=0, minute=0, second=0, tzinfo=beijing_tz)
        return int(dt.timestamp())
    except ValueError:
        # 所有格式都解析失败
        return 0


# 配置字典 - 修正了拼写错误
config_dict = {
    "期刊": {"level_limit": 0, "merge_limit": 1800, "delete_limit": 25},
    "指南": {"level_limit": 0, "merge_limit": 1800, "delete_limit": 25},
    "教科书": {"level_limit": 2, "merge_limit": 1000, "delete_limit": 0}
}

def need_delete_ckwx(text):
    # 判断文本中是否有参考文献、微信订阅等字样
    return any(pattern.search(text) for pattern in REFERENCE_PATTERNS)


def need_delete_english(text):
    # 如果中文字符大于20%，返回False,该数据会被过滤
    if not text:
        return True
    
    # 统计英文字母(a-z,A-Z)的数量
    en_count = len(re.findall(r'[a-zA-Z]', text))
    
    # 统计中文字符(Unicode范围：\u4e00-\u9fff)的数量
    zh_count = len(re.findall(r'[\u4e00-\u9fff]', text))

    # 计算总有效字符数(英文字符+中文字符)
    total = en_count + zh_count
    if total == 0:
        return True
    # 计算中文字符比例
    zh_ratio = zh_count / total
    # 如果中文字符比例>20%,返回False；否则返回True
    return zh_ratio <= 0.2


class TreeNode:
    __slots__ = ('title', 'level', 'content', 'parent', 'children', 'status')  # 使用__slots__减少内存占用
    
    def __init__(self, title, level, content="", parent=None):
        self.title = title
        self.level = level
        self.content = content
        self.parent = parent
        self.children = []
        self.status = "default"

    def add_child(self, child):
        self.children.append(child)

    def __repr__(self):
        result = "    " * self.level + f"{self.title} (L{self.level}) {self.content}\n"
        for child in self.children:
            result += child.__repr__()
        return result

    def get_all_content(self):
        if not self.content:
            return None
            
        result = ""
        current = self
        while current.parent is not None:
            result = current.title + "_" + result
            current = current.parent
        return {"title": result[:-1], "content": self.content, "status": self.status}


def build_tree(strings):
    # 创建虚拟根节点
    root = TreeNode("root", -1)

    for s in strings:
        parts = [p for p in s["title"].split('_') if p]  # 分割并过滤空部分
        current_parent = root

        for i, part in enumerate(parts):
            # 检查是否已存在该节点 - 使用字典优化查找
            existing_node = None
            # 使用字典优化子节点查找
            for child in current_parent.children:
                if child.title == part and child.level == i:
                    existing_node = child
                    break
                    
            if existing_node:
                current_parent = existing_node
            else:
                new_node = TreeNode(part, i, s.get("text_deal", "") if i == len(parts) - 1 else "", current_parent)
                current_parent.add_child(new_node)
                current_parent = new_node
    return root


def process_tree(root, level_limit, merge_limit):
    # 使用迭代而非递归遍历树，避免递归深度限制和函数调用开销
    stack = [root]
    
    while stack:
        node = stack.pop()
        
        # 将子节点加入栈中继续处理
        for child in node.children:
            stack.append(child)
        
        # 检查当前节点是否是次底层节点(即所有子节点都是底层节点)
        if node.children and all(not child.children for child in node.children):
            # 计算所有底层节点内容的总长度
            total_length = len(node.content)
            for child in node.children:
                total_length += len(child.content)
                
            # 如果总长度小于merge_limit,则合并内容
            if total_length < merge_limit and node.level >= level_limit:
                # 收集更新所有子节点内容
                content_parts = [node.content]
                for child in node.children:
                    content_parts.append(child.title + " " + child.content)
                
                node.content = "\n".join(filter(None, content_parts))
                # 删除所有子节点
                node.children = []
                node.status = "merge"
    
    return root


def get_all_data(node):
    result = []
    stack = [node]
    
    while stack:
        current_node = stack.pop()
        content = current_node.get_all_content()
        if content:
            result.append(content)
        # 将子节点加入栈中
        for child in reversed(current_node.children):  # 反转以保持原始顺序
            stack.append(child)
    
    return result


def segmnet_long_doc_0(obj_arr):
    result = []
    for i in obj_arr:
        if len(i["content"]) > 1000 and i["status"] != "merge":
            # 使用预编译的正则表达式分割
            paragraphs = [p.strip() for p in RE_SPLIT_PATTERN.split(i["content"]) if p.strip()]
            merged_paragraphs = []
            current_para = ""
            is_merged = False  # 标记当前段落是否是合并的
            
            # 根据中文标点判断段落是否应该合并
            for para in paragraphs:
                if not para:
                    continue
                    
                # 如果当前段落为空，直接赋值
                if not current_para:
                    current_para = para
                    continue
                
                # 检查当前段落是否以中文标点结尾
                if RE_CHINESE_PUNCTUATION.search(current_para):
                    # 如果当前段落已经以标点结尾，则开始新段落
                    merged_paragraphs.append((current_para, is_merged))
                    current_para = para
                    is_merged = False
                else:
                    # 没有以标点结尾，继续与当前段落合并
                    # 检查是否需要添加空格（只有前后都是英文时才添加）
                    if current_para and para:
                        last_char = current_para[-1]
                        first_char = para[0]
                        
                        # 判断字符是否为英文（字母、数字或常见英文标点）
                        is_last_char_english = RE_ENGLISH_CHAR_CHECK.match(last_char)
                        is_first_char_english = re.match(r'[a-zA-Z0-9(]', first_char) if first_char else False
                        
                        if is_last_char_english and is_first_char_english:
                            current_para += " "
                    
                    current_para += para
                    is_merged = True  # 标记为合并段落
            
            # 处理最后一段
            if current_para:
                merged_paragraphs.append((current_para, is_merged))
            
            # 对合并后的段落进行处理
            for para, merged in merged_paragraphs:
                if merged:
                    # 如果是合并的段落，直接保留，不进行分割，并标记为"merged"
                    result.append({"title": i["title"], "content": para, "status": "merged"})
                elif len(para) > 700:
                    # 对于非合并的长段落，进行分割
                    chunks = chunk_long_doc(para, 700, r"\n", "\n")
                    for chunk in chunks:
                        result.append({"title": i["title"], "content": chunk, "status": "cut"})
                else:
                    result.append({"title": i["title"], "content": para, "status": i["status"]})
        else:
            result.append(i)
    return result


def segmnet_long_doc_1(obj_arr):
    result = []
    for i in obj_arr:
        # 跳过已经合并的段落
        if i.get("status") == "merged":
            result.append(i)
            continue
            
        if len(i["content"]) > 1000 and i["status"] == "cut":
            chunks = chunk_long_doc(i["content"], 650, r"[.。！!？?]", "。")
            chunks_data = [{"title": i["title"], "content": s, "status": "cut"} for s in chunks]
            result.extend(chunks_data)
        else:
            result.append(i)
    return result


def segmnet_long_doc_2(obj_arr):
    result = []
    for i in obj_arr:
        # 跳过已经合并的段落
        if i.get("status") == "merged":
            result.append(i)
            continue
            
        if len(i["content"]) > 1000 and i["status"] == "cut":
            chunks = chunk_long_doc(i["content"], 600, "", "")
            chunks_data = [{"title": i["title"], "content": s, "status": "cut"} for s in chunks]
            result.extend(chunks_data)
        else:
            result.append(i)
    return result


def chunk(data, level_limit, merge_limit, delete_limit):
    # 预处理：过滤不需要的内容
    filtered_data = []
    for i in data:
        # print(i)
        if not need_delete_ckwx(i["title"]) and not need_delete_english(i.get("content", "")):
            filtered_data.append(i)
    
    data = build_tree(filtered_data)
    data = process_tree(data, level_limit, merge_limit)
    data = get_all_data(data)

    data = segmnet_long_doc_0(data)
    data = segmnet_long_doc_1(data)
    data = segmnet_long_doc_2(data)

    # 过滤短内容
    data = [i for i in data if len(i["content"]) > delete_limit]

    

    return data


def format_magazine(data, meta_info, filename):
    result = []
    # 使用文件名作为unique_id
    unique_id = filename
    for chunk_id, i in enumerate(data, 1):
        # 最多分割1次
        parts = i["title"].split('_', 1) 
        # 判断标题的各种情况
        sub_title = parts[1] if len(parts) > 1 else ""

        line = {
            "title": parts[0],
            "sub_title": sub_title,
            "content": i["content"],
            # "url": meta_info.get("url", ""),
            # "url_web": meta_info.get("url_web", ""),
            "unique_id": unique_id,  # 使用文件名作为unique_id
            "post_ts": to_timestamp(meta_info.get("year", "")),
            # "category": meta_info.get("file_type", ""),
            # "publishing": meta_info.get("publishing", ""),
            # "author": meta_info.get("authorlist", ""),
            # "journal_name": meta_info.get("journal_name", ""),
            "chunk_id": chunk_id,
        }
        result.append(line)
    return result


def format_guide_textbook(data, meta_info, filename):
    result = []
    # 使用文件名作为unique_id
    unique_id = filename
    title = meta_info.get("journal_name", "")
    
    for chunk_id, i in enumerate(data, 1):
        # 最多分割1次
        parts = i["title"].split('_', 1) 
        
        # 判断标题的各种情况
        if len(parts) == 1:
            if parts[0] == title:
                sub_title = ""
            else:
                sub_title = i["title"]
        else:
            if parts[0] == title:
                sub_title = parts[1]
            else:
                sub_title = i["title"]

        line = {
            "title": title,
            "sub_title": sub_title,
            "content": i["content"],
            # "url": meta_info.get("url", ""),
            # "url_web": meta_info.get("url_web", ""),
            # "unique_id": unique_id,  # 使用文件名作为unique_id
            # "post_ts": to_timestamp(meta_info.get("pubdate", "")),
            # "category": meta_info.get("file_type", ""),
            # "publishing": meta_info.get("publishing", ""),
            # "author": meta_info.get("authorlist", ""),
            # "journal_name": meta_info.get("journal_name", ""),
            "chunk_id": chunk_id,
        }
        result.append(line)
    return result

def work(level_limit, merge_limit, delete_limit, data):
    # 预编译正则表达式以提高性能
    title_patterns = [
        re.compile(r'参\s*考\s*文\s*献'),  # 按顺序,允许空格
        re.compile(r"微信订阅")
    ]

    chunked_data = chunk(data, level_limit, merge_limit, delete_limit)
    return chunked_data

def strategy_select(data, chunk_model):
    chunk_model = ""
    config = config_dict.get(chunk_model, {"level_limit": 0, "merge_limit": 1800, "delete_limit": 25})
    level_limit = config['level_limit']
    merge_limit = config['merge_limit']
    delete_limit = config['delete_limit']
    return work(level_limit, merge_limit, delete_limit, data)


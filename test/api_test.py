import requests


url = "http://127.0.0.1:8888/chunking/api/v2"

files = ["/data/lynxiao/ai-chunking/chunk/sparkocr_md/AHYX202407016.md"]


with open(files[0], 'r', encoding='utf-8') as f:
    md_content = f.read()

print(md_content)
data = {
    "header":{
        "traceId": "default"
    },
    "payload":{
        "filter_model": "v20250825",
        "chunk_model": "v20250825",
        "texts":[
            {
                "id":"12233",
                "content": md_content
            },
           
        ]
    }
}

print(data)


response = requests.post(url, json=data)

print(response.json())

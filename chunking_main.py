from vendor.pandora.server import fast_server
import config
import sys
sys.path.insert(0, "./vendor")

app = fast_server.get_app()
server_name = config.global_config.get('server', {}).get('name', 'chunking')
log_path = config.global_config.get('server', "/lynxiao/logs/elk.json").get('log_path')
print(server_name,log_path)

fast_server.init_server(server_name,log_path=log_path)
fast_server.add_route("./server/chunking.py","chunking_process",router_path="/chunking/api/v2")
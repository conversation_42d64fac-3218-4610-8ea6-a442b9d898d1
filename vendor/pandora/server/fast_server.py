import uvicorn
import traceback
from pathlib import Path
from typing import Callable, Any,Awaitable
from . import engine
from ..context.pandora_context import <PERSON><PERSON>ontext
from ..middleware.fastapi_middle import ContextMiddleware
from fastapi import FastAP<PERSON>, Request,HTTPException
from ..elk import init_global_logger

app = FastAPI()

def mock(pandora_context: PandoraContext ,request: Request):
    raise NotImplementedError("engine_process is not implemented")

'''
async def engine_infer(request: Request):
    try:
        pc: PandoraContext = request.state.pandora_context
        return engine_process(pc, request)
    except Exception as e:
        print("Error during engine_infer:", e)
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f'Internal Server Error:{e}')

'''    
EngineFuncType = Callable[[PandoraContext, Request], Awaitable[Any]]

def fast_api_wrapper(engine_func:EngineFuncType):
    async def engine_infer(request: Request):
        try:
            pc: PandoraContext = request.state.pandora_context
            return await engine_func(pc, request)
        except Exception as e:
            print("Error during engine_infer:", e)
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f'Internal Server Error:{e}')
    return engine_infer
                            
def init_infer_server(server_name:str ,engine_path: str,func_name="engine_process",router_path="/infer",**kwargs):
    init_global_logger(server_name,**kwargs)
    engine_process = engine.load_function(file_path=engine_path, function_name=func_name)
    # 动态注册 POST 路由
    app.add_api_route(
        path=router_path,
        endpoint=fast_api_wrapper(engine_process),
        methods=["POST"],
    )

    app.add_middleware(ContextMiddleware)

def add_route(engine_path: str,func_name="engine_process",router_path="/infer",methods=["POST"]):

    try:
        engine_process = engine.load_function(file_path=engine_path, function_name=func_name)
        # 动态注册 POST 路由
        app.add_api_route(
            path=router_path,
            endpoint=fast_api_wrapper(engine_process),
            methods=methods,
        )

        print(f"add route {router_path},{engine_path},{func_name},{methods}")
    except Exception as e:
        print(f"add route {router_path},{engine_path},{func_name},{methods} error:{e}")
        raise e

def init_server(server_name:str,**kwargs):
    init_global_logger(server_name,**kwargs)
    app.add_middleware(ContextMiddleware)

def get_module_path() -> str:
    # 当前文件路径
    current_file = Path(__file__).resolve()

    # 项目根目录（可根据实际情况调整，比如找包含 main.py 或 pyproject.toml 的目录）
    project_root = Path.cwd()  # 或者 Path(__file__).resolve().parents[n]

    # 去掉后缀 .py，获得相对模块路径
    relative_path = current_file.relative_to(project_root).with_suffix("")

    # 替换路径分隔符为点号，构成模块导入路径
    module_path = ".".join(relative_path.parts)

    return module_path

def server_start(port: int,workers=1):
    mpath = get_module_path()
    uvicorn.run(f"{mpath}:app", host="0.0.0.0", port=port, workers=workers)

def get_app()->FastAPI:
    return app

def reset_app(nwapp:FastAPI)->FastAPI:
    global app
    app = nwapp
    return app
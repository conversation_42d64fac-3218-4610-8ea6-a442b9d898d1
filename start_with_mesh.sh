#!/usr/bin/env bash
set -e

APP="${1?Usage: $0 <module:app> [port] [workers]}"
PORT="${2:-8080}"
WORKERS="${3:-$(( $(nproc) * 2 + 1 ))}"

echo "→ Starting $APP on 0.0.0.0:$PORT with $WORKERS workers"

gunicorn -k uvicorn.workers.UvicornWorker \
  -w "$WORKERS" \
  --bind "0.0.0.0:$PORT" \
  "$APP" \
  # > python.log 2>&1 &

echo "skynet-mesh start"
# 再启动 skynet-mesh
# /lynxiao/skynet/skynet-mesh server -c /lynxiao/skynet/conf
# ./skynet-runtime server -c ./skynet/conf